'use client';

import { useTranslations } from 'next-intl';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';

export const Breadcrumb = () => {
    const t = useTranslations();
    const pathname = usePathname();
    const router = useRouter();

    const isUUID = (str: string) => {
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        return uuidRegex.test(str);
    };

    let pathSegments = pathname.split('/').filter(segment => segment && !isUUID(segment));

    if (!pathname.startsWith('/dashboard')) {
        pathSegments = ['start', ...pathSegments];
    }

    const generateText = (segment: string) => t(`breadcrumb.${segment}`);

    return (
        <div className="flex items-center text-main-600 text-xs gap-2 cursor-pointer">
            {pathSegments.map((segment, index) => {
                const href = `/${pathSegments.slice(pathSegments[0] === 'start' ? 1 : 0, index + 1).join('/')}`;
                const isLast = index === pathSegments.length - 1;
                const content = isLast ? (
                    <span className="text-main-100 underline">{generateText(segment)}</span>
                ) : (
                    <Link href={href} className="hover:underline">
                        {generateText(segment)}
                    </Link>
                );

                const backArrow = !isLast && (
                    <div onClick={() => router.back()} className="cursor-pointer">
                        <Image src="/assets/BackArrow.svg" alt="back arrow" width={16} height={16} />
                    </div>
                );

                return (
                    <div key={href} className="flex items-center gap-2">
                        {content}
                        {backArrow}
                    </div>
                );
            })}
        </div>
    );
};
