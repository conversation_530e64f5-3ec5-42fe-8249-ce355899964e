apiVersion: v1
data:
    env.json: |
        {
          "API_URL": "https://deploy-stg.evdi.app/api",
          "DEPLOYMENT_TYPE": "EMPE_OVH_K8S_DEPLOYMENT",
          "NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY": "pk_test_51RiysY2XZA2cA55kRnPFx8KUMtwZcDA1zkP00OjWU7rX5BB8emBVQ11lSNDGZ7LRrws7cgKuMfCxS5J6FNMslMT500Lb2Zv4XZ",
          "NEXT_PUBLIC_STRIPE_PRICING_TABLE_ID": "prctbl_1RjeSx2XZA2cA55k5DvZmIVd"
        }
kind: ConfigMap
metadata:
    name: app-config-one-click-deployer-stg
    namespace: customer-issuer-verifier
