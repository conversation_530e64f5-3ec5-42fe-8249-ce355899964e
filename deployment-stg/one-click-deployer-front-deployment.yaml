apiVersion: apps/v1
kind: Deployment
metadata:
    name: one-click-deployer-front-stg
    namespace: customer-issuer-verifier
spec:
    replicas: 1
    selector:
        matchLabels:
            app: one-click-deployer-front-stg
    template:
        metadata:
            labels:
                app: one-click-deployer-front-stg
        spec:
            containers:
                - name: one-click-deployer-front-stg
                  image: 309596z9.c1.gra9.container-registry.ovh.net/empe/services/one-click-deployer-front:0.0.47
                  imagePullPolicy: Always
                  ports:
                      - containerPort: 3000
                  volumeMounts:
                      - name: app-config-one-click-deployer-stg
                        mountPath: /public/config
                        readOnly: true
            imagePullSecrets:
                - name: harbor-registry-secret
            volumes:
                - name: kubeconfig-volume
                  secret:
                      secretName: kubeconfig-secret
                - name: app-config-one-click-deployer-stg
                  configMap:
                      name: app-config-one-click-deployer-stg
