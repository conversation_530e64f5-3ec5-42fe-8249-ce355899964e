import { DeploymentsTypesEnum } from '@/common/deploymentsTypes';
import axiosInstance from '@/middleware/axiosInstance';
import { UpgradeDeploymentSecretResponse } from '@/types/deployments';
import {
    IssuerDeployment,
    IssuerDeploymentCreateRequest,
    IssuerDeploymentCreateResponse,
} from '@/types/deploymentsIssuer';
import { useConfigStore } from '@/store/configStore';
import { type NewIssuerForm } from '@/validation/newIssuerValidation';

const { env } = useConfigStore.getState();
const deploymentType = DeploymentsTypesEnum[env.DEPLOYMENT_TYPE as keyof typeof DeploymentsTypesEnum];

const URI_PREFIX = '/deployment/issuer';

export const handleGetUserIssuerDeployments = async (): Promise<IssuerDeployment[]> => {
    const response = await axiosInstance.get(`${URI_PREFIX}/get/me`);
    return response.data;
};

export const handleGetIssuerDeploymentById = async (id: string): Promise<IssuerDeployment> => {
    const response = await axiosInstance.get(`${URI_PREFIX}/${id}`);
    return response.data;
};

export const handleCreateIssuerDeployment = async (props: NewIssuerForm): Promise<IssuerDeploymentCreateResponse> => {
    const data: IssuerDeploymentCreateRequest = {
        ...props,
        deploymentType: deploymentType,
    };

    const response = await axiosInstance.post(`${URI_PREFIX}/create`, data);
    return response.data;
};

export const handleDeleteIssuerDeployment = async (id: string): Promise<void> => {
    const response = await axiosInstance.delete(`${URI_PREFIX}/${id}`);
    return response.data;
};

export const handleUpgradeIssuerDeploymentSecret = async (id: string): Promise<UpgradeDeploymentSecretResponse> => {
    const response = await axiosInstance.patch(`${URI_PREFIX}/${id}/update-client-secret`);
    return response.data;
};
