import { handleGetUser } from '@/api/user';
import { RouterPaths } from '@/common/routerPaths';
import { useDeploymentsStore } from '@/store/deploymentsStore';
import { useUserStore } from '@/store/userStore';
import { createNewIssuerSchema, NewIssuerForm } from '@/validation/newIssuerValidation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { useModal } from './useModal';
import { useTranslations } from 'next-intl';
import { handleCreateIssuerDeployment, handleGetUserIssuerDeployments } from '@/api/deploymentsIssuer';
import { ModalSuccessCreateNewIssuer } from '@/components/Modals';
import { useErrorHandling } from './useErrorHandling';
import { ErrorCreateNewIssuer } from '@/errors/ErrorCreateNewIssuer';
import { useEffect } from 'react';

interface Props {
    defaultValues: Partial<NewIssuerForm>;
}

export const useNewIssuer = ({ defaultValues }: Props) => {
    const t = useTranslations();
    const schema = createNewIssuerSchema(t);
    const { withErrorHandling } = useErrorHandling();
    const { showModal } = useModal();
    const router = useRouter();
    const {
        register,
        handleSubmit,
        trigger,
        watch,
        control,
        reset,
        formState: { errors, isValid, isSubmitting },
    } = useForm<NewIssuerForm>({
        resolver: zodResolver(schema),
        mode: 'all',
        reValidateMode: 'onChange',
        defaultValues,
    });
    const formFields = schema.shape;
    const { setDeployments } = useDeploymentsStore();
    const { setUser } = useUserStore();

    useEffect(() => {
        reset(defaultValues);
    }, [defaultValues, reset]);

    const onSubmit = (data: NewIssuerForm) =>
        withErrorHandling(async () => {
            try {
                const response = await handleCreateIssuerDeployment(data);
                const deployments = await handleGetUserIssuerDeployments();
                const user = await handleGetUser();
                setDeployments(deployments);
                setUser(user);
                showModal(<ModalSuccessCreateNewIssuer authKey={response.authKey} />);
                router.push(RouterPaths.DASHBOARD);
            } catch (error) {
                throw new ErrorCreateNewIssuer(error);
            }
        });

    const watchBlockchainConfigurationId = watch('blockchainConfigurationId');

    return {
        isSubmitting,
        trigger,
        formFields,
        register,
        handleSubmit,
        errors,
        isValid,
        onSubmit,
        watchBlockchainConfigurationId,
        control,
    };
};
