import { fetchDeploymentsMethods } from '@/common/fetchMethods';
import { useUserStore } from '@/store/userStore';
import { DeploymentType } from '@/types/deployments';
import { IssuerDeployment } from '@/types/deploymentsIssuer';
import { VerifierDeployment } from '@/types/deploymentsVerifier';
import { useState, useEffect } from 'react';
import { useDecoratedBlockchainConfigsSelect } from './useDecoratedBlockchainConfigs';
import { useGetAvailableBlockchainConfigs } from './useGetAvailableBlockchainConfigs';

interface Props {
    type: DeploymentType;
}

export const useGetDeploymentCreditsLeft = ({ type }: Props) => {
    const { blockchainConfigs } = useGetAvailableBlockchainConfigs();

    const { user } = useUserStore();

    const [deployments, setDeployments] = useState<VerifierDeployment[] | IssuerDeployment[]>([]);

    useEffect(() => {
        (async () => {
            try {
                const data = await fetchDeploymentsMethods[type]();
                setDeployments(data);
            } catch (err) {
                console.error('Failed to fetch deployments', err);
            }
        })();
    }, []);

    const decoratedConfigs = useDecoratedBlockchainConfigsSelect(
        blockchainConfigs,
        user,
        DeploymentType.ISSUER,
        deployments
    );

    return {
        decoratedConfigs,
    };
};
