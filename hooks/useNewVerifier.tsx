import { handleGetUser } from '@/api/user';
import { RouterPaths } from '@/common/routerPaths';
import { useDeploymentsStore } from '@/store/deploymentsStore';
import { useUserStore } from '@/store/userStore';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { useModal } from './useModal';
import { useTranslations } from 'next-intl';
import { createNewVerifierSchema, NewVerifierForm } from '@/validation/newVerifierValidation';
import { useErrorHandling } from './useErrorHandling';
import { ErrorCreateNewVerifier } from '@/errors/ErrorCreateNewVerifier';
import { handleCreateVerifierDeployment, handleGetUserVerifierDeployments } from '@/api/deploymentsVerifier';
import { ModalSuccessCreateNewVerifier } from '@/components/Modals';
import { useEffect } from 'react';

interface Props {
    defaultValues: Partial<NewVerifierForm>;
}

export const useNewVerifier = ({ defaultValues }: Props) => {
    const t = useTranslations();
    const { withErrorHandling } = useErrorHandling();
    const schema = createNewVerifierSchema(t);
    const { showModal } = useModal();
    const router = useRouter();
    const {
        register,
        handleSubmit,
        trigger,
        watch,
        control,
        reset,
        formState: { errors, isValid, isSubmitting },
    } = useForm<NewVerifierForm>({
        resolver: zodResolver(schema),
        mode: 'all',
        reValidateMode: 'onChange',
        defaultValues,
    });
    const formFields = schema.shape;
    const { setDeployments } = useDeploymentsStore();
    const { setUser } = useUserStore();

    useEffect(() => {
        reset(defaultValues);
    }, [defaultValues, reset]);

    const onSubmit = (data: NewVerifierForm) =>
        withErrorHandling(async () => {
            try {
                const response = await handleCreateVerifierDeployment(data);
                const deployments = await handleGetUserVerifierDeployments();
                const user = await handleGetUser();
                setDeployments(deployments);
                setUser(user);
                showModal(<ModalSuccessCreateNewVerifier authKey={response.authKey} />);
                router.push(RouterPaths.DASHBOARD);
            } catch (error) {
                throw new ErrorCreateNewVerifier(error);
            }
        });

    const watchBlockchainConfigurationId = watch('blockchainConfigurationId');

    return {
        trigger,
        formFields,
        register,
        handleSubmit,
        errors,
        isValid,
        isSubmitting,
        watchBlockchainConfigurationId,
        onSubmit,
        control,
    };
};
