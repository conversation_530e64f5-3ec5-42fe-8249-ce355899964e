import { PasswordData } from '@/types/validationMethods';
import { z } from 'zod';

export const validateDeploymentName = (t: (key: string) => string) => {
    return z
        .string()
        .min(1, t('validation_messages.name_required'))
        .min(3, t('validation_messages.deployment_name_min_length'))
        .regex(/^[a-z][a-z0-9_-]*[a-z0-9]$/, t('validation_messages.deployment_name_format'))
        .refine(value => /^[a-z]/.test(value), { message: t('validation_messages.deployment_name_start_lowercase') })
        .refine(value => /[a-z0-9]$/.test(value), {
            message: t('validation_messages.deployment_name_end_alphanumeric'),
        })
        .refine(value => /^[a-z0-9_-]+$/.test(value), {
            message: t('validation_messages.deployment_name_invalid_chars'),
        });
};

export const validateName = (t: (key: string) => string) => {
    return z
        .string()
        .min(1, t('validation_messages.name_required'))
        .min(3, t('validation_messages.name_min_length'))
        .regex(/^[a-zA-Z]+$/, t('validation_messages.name_invalid_format'))
        .refine(value => /^[a-zA-Z]+$/.test(value), { message: t('validation_messages.name_only_letters') });
};

export const validateOrganizationName = (t: (key: string) => string) => {
    return z
        .string()
        .min(1, t('validation_messages.organization_name_required'))
        .refine(value => value.trim().length > 0, { message: t('validation_messages.organization_name_empty') });
};

export const validateNameWithUppercase = (t: (key: string) => string) => {
    return z
        .string()
        .min(1, t('validation_messages.name_required'))
        .min(3, t('validation_messages.name_with_uppercase_min_length'))
        .regex(/^[a-zA-Z][a-zA-Z0-9\s_-]*[a-zA-Z0-9]$/, t('validation_messages.name_with_uppercase_format'))
        .refine(value => /^[a-zA-Z]/.test(value), {
            message: t('validation_messages.name_with_uppercase_start_letter'),
        })
        .refine(value => /[a-zA-Z0-9]$/.test(value), {
            message: t('validation_messages.name_with_uppercase_end_alphanumeric'),
        })
        .refine(value => /^[a-zA-Z0-9\s_-]+$/.test(value), {
            message: t('validation_messages.name_with_uppercase_invalid_chars'),
        });
};

export const validateEmail = (t: (key: string) => string) => {
    return z
        .string()
        .min(1, t('validation_messages.email_required'))
        .email(t('validation_messages.email_invalid_format'))
        .refine(value => value.trim().length > 0, { message: t('validation_messages.email_empty') });
};

export const validatePassword = (t: (key: string) => string) => {
    return z
        .string()
        .min(1, t('validation_messages.password_required'))
        .min(8, t('validation_messages.password_too_short'))
        .refine(value => value.trim().length > 0, { message: t('validation_messages.password_empty') });
};

export const validateRepeatPassword = (t: (key: string) => string) => {
    return z
        .string()
        .min(1, t('validation_messages.repeat_password_required'))
        .refine(value => value.trim().length > 0, { message: t('validation_messages.repeat_password_empty') });
};

export const validateVersion = (t: (key: string) => string) => {
    return z
        .union([
            z
                .string()
                .min(1, t('validation_messages.version_required'))
                .refine(value => value.trim().length > 0, { message: t('validation_messages.version_empty') })
                .refine(value => /^v?(\d+\.\d+(\.\d+)?)$/.test(value), {
                    message: t('validation_messages.version_invalid_format'),
                }),
            z
                .number()
                .refine(value => !isNaN(value), { message: t('validation_messages.version_number_invalid') })
                .transform(num => num.toString()),
        ])
        .refine(value => value.length > 0, {
            message: t('validation_messages.version_empty'),
        });
};

export const validateCheckbox = (t: (key: string) => string) => {
    return z.boolean().refine(value => value === true, {
        message: t('validation_messages.checkbox_must_be_checked'),
    });
};

export const validateVersionId = (t: (key: string) => string) => {
    return z
        .union([
            z
                .number()
                .min(1, t('validation_messages.version_id_must_be_positive'))
                .refine(value => !isNaN(value), { message: t('validation_messages.version_id_invalid_number') }),
            z
                .string()
                .min(1, t('validation_messages.version_id_empty'))
                .refine(value => value.trim().length > 0, { message: t('validation_messages.version_id_empty') })
                .refine(value => !isNaN(parseInt(value, 10)), {
                    message: t('validation_messages.version_id_invalid_number'),
                })
                .transform(str => parseInt(str, 10)),
        ])
        .refine(value => !isNaN(Number(value)) && Number(value) >= 1, {
            message: t('validation_messages.version_id_must_be_positive'),
        });
};

export const validatePasswordsMatch = (t: (key: string) => string) => (data: PasswordData, ctx: z.RefinementCtx) => {
    if (data.password !== data.repeatPassword) {
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['repeatPassword'],
            message: t('validation_messages.passwords_must_match'),
        });
    }
};

export const validateBlockchainConfig = (t: (key: string) => string) => {
    return z
        .number()
        .min(1, t('validation_messages.blockchain_config_must_be_positive'))
        .refine(value => !isNaN(value), { message: t('validation_messages.blockchain_config_invalid_number') });
};

// New validator for phone number (E.164-like, 7-25 characters)
export const validatePhoneNumber = (t: (key: string) => string) => {
    return z
        .string()
        .min(1, t('validation_messages.phone_number_required'))
        .regex(/^\+?[0-9. ()-]{7,25}$/, t('validation_messages.phone_number_invalid'))
        .refine(value => value.trim().length > 0, { message: t('validation_messages.phone_number_empty') });
};

// Validator for readable name (3-100 characters, non-empty, max length only)
export const validateReadableName = (t: (key: string) => string) => {
    return z
        .string()
        .min(1, t('validation_messages.readable_name_required'))
        .min(3, t('validation_messages.readable_name_min_length'))
        .max(100, t('validation_messages.readable_name_max_length'));
};