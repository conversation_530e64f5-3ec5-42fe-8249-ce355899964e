'use client';

import { useTranslations } from 'next-intl';
import { useSearchParams } from 'next/navigation';
import { useState } from 'react';
import { ButtonGradient } from '@/components/Buttons';
import { renderFormFields } from '@/common/renderFormFields';
import { useRequestPasswordResetForm } from '@/hooks/useRequestPasswordResetForm';
import { RequestPasswordResetFormInputsTypes } from '@/validation/requestPasswordResetValidation';
import { ItemBox } from '@/components/ItemBox';
import Link from 'next/link';
import { RouterPaths } from '@/common/routerPaths';

const RequestPasswordReset = () => {
    const t = useTranslations('request_password_reset');
    const searchParams = useSearchParams();

    const emailFromParams = searchParams.get('email');

    const [requestSent, setRequestSent] = useState(false);
    const [submittedEmail, setSubmittedEmail] = useState<string | null>(emailFromParams);

    const { formFields, register, handleSubmit, errors, control, handleFormSubmit, isEmailDisabled } =
        useRequestPasswordResetForm({
            email: emailFromParams || undefined,
            onSuccess: data => {
                setSubmittedEmail(data.email);
                setRequestSent(true);
            },
        });

    if (requestSent) {
        return (
            <ItemBox>
                <div className="w-box-100 h-box-100 px-box-100 py-box-100 flex flex-col justify-between">
                    <label className="text-2xl">{t('success_label')}</label>
                    <div className="flex flex-col gap-3 flex-1 justify-center">
                        <h1 className="text-2xl font-bold">{t('success_title')}</h1>
                        <p className="">
                            {t('success_description_part1')}
                            {submittedEmail && <span className="font-bold text-main-100"> {submittedEmail}</span>}
                        </p>
                    </div>
                </div>
            </ItemBox>
        );
    }

    const fieldsToRender = renderFormFields({
        formFields,
        register,
        errors,
        t,
        formInputsTypes: RequestPasswordResetFormInputsTypes,
        control,
        disabledFields: {
            email: isEmailDisabled,
        },
    });

    const renderFooter = () => {
        if (isEmailDisabled) return null;
        return (
            <div className="flex flex-row items-center text-sm justify-center whitespace-nowrap gap-1">
                Do you remember your password?
                <Link href={RouterPaths.LOGIN} className="text-main-100 underline">
                    Return to login
                </Link>
            </div>
        );
    };

    return (
        <ItemBox>
            <div className="flex flex-col justify-evenly w-box-100 h-box-100 px-box-100 py-box-50">
                <h1 className="text-2xl text-left">{t('title')}</h1>
                <form noValidate>{fieldsToRender}</form>
                <ButtonGradient onClick={handleSubmit(handleFormSubmit)}>{t('button')}</ButtonGradient>
                {renderFooter()}
            </div>
        </ItemBox>
    );
};

export default RequestPasswordReset;
