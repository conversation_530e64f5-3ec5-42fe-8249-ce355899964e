import { DashboardOptionBar } from '@/components/DashboardOptionBar';
import { SideBar } from '@/components/SideBar';
import { DeploymentsTableTabsProvider } from '@/contexts/DeploymentsTableTabsContext';
import Script from 'next/script';

export default async function DashboardLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <div className="flex flex-row">
            <Script
                id="ze-snippet"
                strategy="afterInteractive"
                src="https://static.zdassets.com/ekr/snippet.js?key=bbb92d84-74f1-41d0-a90d-74bd558bbc72"
            >
                {' '}
            </Script>
            <SideBar />
            <div className="w-full ml-52">
                <DeploymentsTableTabsProvider>
                    <div className="p-box-50">
                        <DashboardOptionBar />
                        {children}
                    </div>
                </DeploymentsTableTabsProvider>
            </div>
        </div>
    );
}
