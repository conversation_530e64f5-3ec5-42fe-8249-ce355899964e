'use client';
import { ItemBox } from '@/components/ItemBox';
import { WidgetDeploymentsTable } from '@/components/WidgetDeploymentsTable/WidgetDeploymentsTable';
import { useDashboardLogic } from '@/hooks/useDashboardLogic';

const Dashboard = () => {
    const { deployments, isLoading } = useDashboardLogic();

    return (
        <div className="relative overflow-hidden w-full h-full">
            <ItemBox>
                <WidgetDeploymentsTable isLoading={isLoading} deployments={deployments} />
            </ItemBox>
        </div>
    );
};

export default Dashboard;
