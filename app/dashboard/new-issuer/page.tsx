'use client';

import { renderFormFields } from '@/common/renderFormFields';
import { ButtonGradient } from '@/components/Buttons';
import { ItemBox } from '@/components/ItemBox';
import { useGetAvailableVersions } from '@/hooks/useGetAvailableVersions';
import { useNewIssuer } from '@/hooks/useNewIssuer';
import { DeploymentType } from '@/types/deployments';
import { NewIssuerFormInputsTypes } from '@/validation/newIssuerValidation';
import { useTranslations } from 'next-intl';
import { useGetDeploymentCreditsLeft } from '@/hooks/useGetDeploymentCreditsLeft';
import { LoaderSpinnerSmall } from '@/components/Loaders';
import { useUserStore } from '@/store/userStore';
import { useMemo } from 'react';

const LOCALE_PREFIX = 'new_issuer';
const NETWORK_PREFIX = {
    1: '-testnet',
    2: '-mainnet',
};

const NewIssuer = () => {
    const t = useTranslations(LOCALE_PREFIX);
    const { user } = useUserStore();

    const { versions } = useGetAvailableVersions({
        type: DeploymentType.ISSUER,
    });

    const defaultValues = useMemo(
        () => ({
            organizationName: user?.organizationName || '',
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            versionId: versions[0]?.value as any,
        }),
        [user?.organizationName, versions]
    );

    const {
        trigger,
        formFields,
        watchBlockchainConfigurationId,
        register,
        handleSubmit,
        errors,
        isValid,
        onSubmit,
        isSubmitting,
        control,
    } = useNewIssuer({
        defaultValues,
    });

    const { decoratedConfigs } = useGetDeploymentCreditsLeft({ type: DeploymentType.ISSUER });

    if (versions.length === 0) {
        return (
            <div className="px-10 h-full flex justify-center items-center">
                <div className="w-full max-w-xl flex justify-center items-center">
                    <LoaderSpinnerSmall />
                </div>
            </div>
        );
    }

    const fieldsToRender = renderFormFields({
        formFields,
        register,
        errors,
        t,
        formInputsTypes: NewIssuerFormInputsTypes,
        trigger,
        control,
        postfix: {
            issuerName: !watchBlockchainConfigurationId
                ? '-issuer'
                : `${NETWORK_PREFIX[watchBlockchainConfigurationId as keyof typeof NETWORK_PREFIX]}-issuer`,
        },
        defaultValues,
        selectOptions: {
            versionId: versions,
            blockchainConfigurationId: decoratedConfigs,
        },
    });

    return (
        <div className="px-10 h-full flex justify-center items-center">
            <div className="w-full max-w-xl">
                <ItemBox>
                    <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4 p-6">
                        <h1 className="text-2xl text-left pb-4">{t('title')}</h1>
                        {fieldsToRender}
                        <ButtonGradient disabled={!isValid} style={{ marginTop: 24 }} isLoading={isSubmitting}>
                            {t('create_button')}
                        </ButtonGradient>
                    </form>
                </ItemBox>
            </div>
        </div>
    );
};

export default NewIssuer;
