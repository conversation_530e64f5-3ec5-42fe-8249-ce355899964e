import { DeploymentsTypesEnum } from '@/common/deploymentsTypes';
import { BlockchainNetworkName } from '@/types/blockchanConfigs';

export enum DeploymentStatus {
    INIT = 'INIT',
    PENDING = 'PENDING',
    FAILED = 'FAILED',
    ACTIVE = 'ACTIVE',
    DELETED_SCHEDULED = 'DELETED_SCHEDULED',
    UPGRADE = 'UPGRADE',
    FAILED_DELETION = 'FAILED_DELETION',
    READY_TO_PUT_TO_BLOCKCHAIN = 'READY_TO_PUT_TO_BLOCKCHAIN',
    TOKEN_ON_ISSUER = 'TOKEN_ON_ISSUER',
}

export enum DeploymentType {
    ISSUER = 'issuer',
    VERIFIER = 'verifier',
}

export const DeploymentStatusColors = {
    [DeploymentStatus.INIT]: 'bg-status-init',
    [DeploymentStatus.PENDING]: 'bg-status-pending',
    [DeploymentStatus.FAILED]: 'bg-status-failed',
    [DeploymentStatus.ACTIVE]: 'bg-status-active',
    [DeploymentStatus.DELETED_SCHEDULED]: 'bg-status-deleted',
    [DeploymentStatus.UPGRADE]: 'bg-status-pending',
    [DeploymentStatus.FAILED_DELETION]: 'bg-status-pending',
    [DeploymentStatus.READY_TO_PUT_TO_BLOCKCHAIN]: 'bg-status-pending',
    [DeploymentStatus.TOKEN_ON_ISSUER]: 'bg-status-pending',
};

export interface deploymentsParams {
    type: DeploymentType;
}

export interface getDeploymentsParams extends deploymentsParams {
    id: number;
}

export interface createDeploymentParams extends deploymentsParams {
    name: string;
}

export type DeploymentCreateRequest = {
    deploymentType: DeploymentsTypesEnum;
} & (
    | {
          issuerName: string;
      }
    | {
          verifierName: string;
      }
);

export type Deployment = {
    id: string;
    fullHost: string;
    status: DeploymentStatus;
    userId: number;
    deploymentType: DeploymentsTypesEnum;
    networkName: BlockchainNetworkName;
    version: string;
    didDocument: string; // usunąć optional
} & (
    | {
          issuerName: string;
          issuerReadableName: string;
          verifierReadableName?: never;
          verifierName?: never;
          issuerCreatorAddress: string; // usunąć optional
          verifierCreatorAddress?: never;
          organization: string; // dodać
      }
    | {
          issuerName?: never;
          issuerReadableName?: never;
          verifierReadableName: string;
          verifierName: string;
          issuerCreatorAddress?: never;
          verifierCreatorAddress: string; // dodać
          organization?: never;
      }
);

export type UpgradeDeploymentSecretResponse = {
    newClientSecret: string;
};
